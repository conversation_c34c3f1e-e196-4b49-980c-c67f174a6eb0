import { Plus, <PERSON>u, X, Ch<PERSON><PERSON>R<PERSON>, MessageSquare, Calendar, Sun, Moon, Sunset, Zap, ListTodo, ChevronDown, ArrowUpDown, Pin, Pin<PERSON>ff, Star, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Info } from "lucide-react";
import { useState, useEffect, useRef, useMemo } from "react";
import { ChatInterface } from "../components/ChatInterface";
import { SearchBar } from "../components/SearchBar";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ChatHistory, Comment } from "@/types/chat";
// import { ChatHistorySection } from "@/components/ChatHistorySection"; // Not used
import { Link, useNavigate, useParams } from "react-router-dom";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SharePopup } from "@/components/SharePopup";
import { useSharePopup } from "@/hooks/use-share-popup";
import { cn } from "@/lib/utils";
import { DiscussionSection } from "@/components/DiscussionSection";
import React from "react";
import { useInView } from "framer-motion";
import { motion } from "framer-motion";
import { FeedbackWidget } from '@/components/FeedbackWidget';
import { generateChatId } from "@/utils/chatUtils";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Array of inspirational quotes with Indian patriotic themes
const inspirationalQuotes = [
  "Keep pushing! Every problem solved brings you closer to your goals 🎯",
  "Study buddies for life! We're tackling these challenges together 📚",
  "One more question, one step closer to that dream college! You've got this 💪",
  "Physics, Chemistry, Math - we'll conquer them all together!",
  "The road to success is paved with solved problems. Let's add more today!",
  "You're making amazing progress! Each doubt cleared is a victory 🏆",
  "Struggling with a concept? That's just your brain getting stronger!",
  "Great minds discuss problems. Let's solve yours today!",
  "Your future self will thank you for the hard work you're putting in now 🌟",
  "Remember: difficult roads often lead to beautiful destinations 🚀",
  "Learn as if you will live forever, live like you will die tomorrow.",
  "To succeed in your mission, you must have single-minded devotion to your goal."
];

const AI = () => {
  useDocumentTitle("AI Assistant - IsotopeAI");
  const { user } = useSupabaseAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [initialQuery, setInitialQuery] = useState("");
  const [initialImage, setInitialImage] = useState<File | undefined>();
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [greeting, setGreeting] = useState("");
  const [quote, setQuote] = useState("");
  const [discussionVisible, setDiscussionVisible] = useState(false);
  const [footerVisible, setFooterVisible] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const { isOpen: isShareOpen, closePopup: closeShare } = useSharePopup();
  const discussionRef = React.useRef<HTMLDivElement>(null);
  const footerRef = useRef(null);
  const isFooterInView = useInView(footerRef, { once: true });
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "name">("newest");
  const navigate = useNavigate();
  const { chatId: chatIdFromParams } = useParams<{ chatId?: string }>();
  const [isNewChatInProgress, setIsNewChatInProgress] = useState(false);

  const handleChatInitialized = () => {
    setIsNewChatInProgress(false);
  };

  // Load user's chat history from Firebase
  useEffect(() => {
    if (!user) return;

    const loadUserChatHistory = async () => {
      try {
        setIsLoading(true);
        const userChats = (await getUserChatHistory(user.uid)) as any[];
        const typedChats: ChatHistory[] = userChats.map((chat) => ({
          id: chat.id,
          timestamp: chat.timestamp || Date.now(),
          messages: Array.isArray(chat.messages) ? chat.messages : [],
          preview: typeof chat.preview === 'string' ? chat.preview : chat.id || "New Chat",
          isStarred: Boolean(chat.isStarred),
          isPinned: Boolean(chat.isPinned),
          comments: Array.isArray(chat.comments) ? chat.comments : [],
        }));
        typedChats.sort((a, b) => b.timestamp - a.timestamp);
        setChatHistory(typedChats);
      } catch (error) {
        console.error("Error loading chat history:", error);
        toast({
          title: "Error",
          description: "Failed to load chat history. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadUserChatHistory();
  }, [user, toast]);

  // Effect to handle chat loading based on URL parameter
  useEffect(() => {
   if (chatIdFromParams && chatHistory.length > 0) {
     const chatExists = chatHistory.some(chat => chat.id === chatIdFromParams);
     if (chatExists) {
       if (currentChatId !== chatIdFromParams) {
         loadChat(chatIdFromParams, true);
       }
     } else {
       toast({
         title: "Chat not found",
         description: `The chat with ID "${chatIdFromParams}" was not found in your history.`,
         variant: "destructive"
       });
       navigate("/ai", { replace: true });
       handleNewChat();
     }
   } else if (!chatIdFromParams && !currentChatId) {
     handleNewChat();
   }
 }, [chatIdFromParams, chatHistory, user, navigate, toast, currentChatId]);


  // Set greeting based on time of day
  useEffect(() => {
    const hour = new Date().getHours();
    let greetingText = "";

    if (hour >= 5 && hour < 12) {
      greetingText = "Good Morning";
    } else if (hour >= 12 && hour < 18) {
      greetingText = "Good Afternoon";
    } else {
      greetingText = "Good Evening";
    }

    setGreeting(`${greetingText}`);

    // Set random inspirational quote
    const randomQuote = inspirationalQuotes[Math.floor(Math.random() * inspirationalQuotes.length)];
    setQuote(randomQuote);
  }, [user]);

  // Handle PWA updates
  useEffect(() => {
    const handleControllerChange = () => {
      setUpdateAvailable(true);
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
    }

    return () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
      }
    };
  }, []);

  // Add scroll event listener to show discussion section and footer on scroll
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // Show discussion section if:
      // 1. User has scrolled more than 300px, OR
      // 2. User has scrolled more than 30% of viewport height, OR
      // 3. User has scrolled more than 20% of the total scrollable area
      const scrollableArea = documentHeight - windowHeight;
      const scrollPercentage = scrollPosition / scrollableArea;

      if (
        scrollPosition > 300 ||
        scrollPosition > windowHeight * 0.3 ||
        scrollPercentage > 0.2
      ) {
        setDiscussionVisible(true);
      }

      // Show footer when user has scrolled more than 60% of the way down
      if (scrollPercentage > 0.6) {
        setFooterVisible(true);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Check initial scroll position
    handleScroll();

    // Add a timeout to show the discussion after 4 seconds even without scrolling
    const timeout = setTimeout(() => {
      setDiscussionVisible(true);
    }, 4000);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeout);
    };
  }, []);

  // Effect to navigate to the correct chat URL when currentChatId changes
  useEffect(() => {
    if (currentChatId && !window.location.pathname.includes('/fullscreen')) {
      const targetPath = `/ai/${currentChatId}`;
      if (window.location.pathname !== targetPath) {
        // It's good practice to log such navigations for debugging during development
        console.log(`Navigating to ${targetPath} due to currentChatId change or URL mismatch.`);
        navigate(targetPath);
      }
    }
  }, [currentChatId, navigate]);

  // Apply sorting to chat history
  const sortedChatHistory = useMemo(() => {
    return [...chatHistory].sort((a, b) => {
      if (sortBy === "newest") {
        return b.timestamp - a.timestamp;
      } else if (sortBy === "oldest") {
        return a.timestamp - b.timestamp;
      } else {
        // Sort by name (preview)
        return a.preview.localeCompare(b.preview);
      }
    });
  }, [chatHistory, sortBy]);

  const handleSearchSubmit = async (query: string, image?: File) => {
    console.log("handleSearchSubmit: Starting. currentChatId:", currentChatId, "chatIdFromParams:", chatIdFromParams);
    if (!user) return;
    setIsNewChatInProgress(true);

    try {
      // Indicate loading state
      setIsLoading(true);

      // Add default text for image-only queries
      const queryText = (!query.trim() && image) ? "Answer according to the image" : query;

      // First create a deterministic chat ID based on the question
      const newChatId = await generateChatId(queryText);

      // Set the current chat ID immediately to trigger loading
      setCurrentChatId(newChatId);

      // Update URL to include the chat ID immediately
      if (!window.location.pathname.includes('/fullscreen')) {
        navigate(`/ai/${newChatId}`);
      }

      // Set this first before showing the chat interface
      setInitialQuery(queryText);
      setInitialImage(image);

      // The useEffect hook listening to chatIdFromParams will handle setting
      // currentChatId and isChatVisible and loading the chat based on the URL.

      // Increment questions asked count and update streak
      await incrementQuestionsAsked(user.uid);

      // Prepare the message object without undefined values
      const messageObj: any = {
        content: queryText,
        isUser: true,
      };

      // Only add image property if there is an image
      if (image) {
        // Only save basic image info here - the full Cloudinary URL
        // will be set by ChatInterface after upload
        messageObj.image = {
          name: image.name,
          // We don't set URL or publicId here as the image hasn't been uploaded to
          // Cloudinary yet. ChatInterface will handle that and update Firebase.
        };
      }

      // Save initial question to Firebase
      await saveAIChat(newChatId, {
        userId: user.uid,
        messages: [messageObj],
        timestamp: Date.now(),
        preview: queryText.substring(0, 100),
        isPinned: false,
        isStarred: false
      }, true);

      // Set a flag for ChatInterface component to know this message has been processed
      sessionStorage.setItem('messageInitiallyProcessed', 'true');
      sessionStorage.setItem('chatQuery', queryText);
      if (image) {
        sessionStorage.setItem('hasImage', 'true');
      }

      // Brief delay to ensure Firebase save completes and states are updated
      await new Promise(resolve => setTimeout(resolve, 300));

      // Add the new chat to the local state immediately
      const newChatEntry: ChatHistory = {
        id: newChatId,
        timestamp: Date.now(), // Consistent with timestamp in saveAIChat
        messages: [messageObj],
        preview: queryText.substring(0, 100),
        isStarred: false,
        isPinned: false,
        comments: []
      };
      setChatHistory(prevHistory => [newChatEntry, ...prevHistory]);

      setIsLoading(false);

    } catch (error) {
      console.error('Error updating user stats or saving question:', error);
      setIsLoading(false);
      toast({
        title: "Error",
        description: "Failed to process your question. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleNewChat = () => {
    setCurrentChatId(null);
    setInitialQuery("");
    setInitialImage(undefined);
    setIsNewChatInProgress(false);
    sessionStorage.removeItem('messageInitiallyProcessed');
    sessionStorage.removeItem('chatQuery');
    sessionStorage.removeItem('hasImage');

    if (chatIdFromParams) {
      navigate("/ai");
    }
    toast({
      title: "New Chat",
      description: "Started a new chat session",
    });
  };

  const loadChat = async (chatId: string, isInitialLoadFromUrl = false) => {
    if (isLoading) return; // Don't load if already loading history
    setIsNewChatInProgress(false);

    const chat = chatHistory.find((c) => c.id === chatId);
    if (chat) {
      setInitialQuery(""); // Reset initial query/image from search bar
      setInitialImage(undefined);
      sessionStorage.removeItem('messageInitiallyProcessed');

      setCurrentChatId(chatId);
      // setSelectedChatId(chatId); // Removed
      setSidebarOpen(false); // Close sidebar if open

      // Only navigate if not an initial load or if the URL doesn't match
      if (!isInitialLoadFromUrl || window.location.pathname !== `/ai/${chatId}`) {
         if (!window.location.pathname.includes('/fullscreen')) {
            navigate(`/ai/${chatId}`);
         }
      }
      if (!isInitialLoadFromUrl) { // Don't toast if loading from URL silently
        toast({
          title: "Previous Chat Loaded",
          description: "You can continue your conversation from where you left off.",
        });
      }
    } else if (chatHistory.length > 0) { // Only show error if chat history is loaded
      toast({
        title: "Error",
        description: `Chat with ID ${chatId} not found.`,
        variant: "destructive",
      });
      navigate("/ai", { replace: true }); // Go back to base /ai page
      handleNewChat();
    }
  };

  const toggleStarChat = async (chatId: string) => {
    try {
      // Find chat and toggle star status
      const chat = chatHistory.find(c => c.id === chatId);
      if (!chat) return;

      const newStarredStatus = !chat.isStarred;

      // Update in Firebase
      await toggleChatStarred(chatId, newStarredStatus);

      // Update local state
      const updatedHistory = chatHistory.map(chat => {
        if (chat.id === chatId) {
          return { ...chat, isStarred: newStarredStatus };
        }
        return chat;
      });

      setChatHistory(updatedHistory);
      toast({
        title: "Chat Updated",
        description: `Chat ${newStarredStatus ? 'starred' : 'unstarred'} successfully.`,
      });
    } catch (error) {
      console.error('Error toggling star status:', error);
      toast({
        title: "Error",
        description: "Failed to update chat. Please try again.",
        variant: "destructive",
      });
    }
  };

  const togglePinChat = async (chatId: string) => {
    try {
      // Find chat and toggle pin status
      const chat = chatHistory.find(c => c.id === chatId);
      if (!chat) return;

      const newPinnedStatus = !chat.isPinned;

      // Update in Firebase
      await toggleChatPinned(chatId, newPinnedStatus);

      // Update local state
      const updatedHistory = chatHistory.map(chat => {
        if (chat.id === chatId) {
          return { ...chat, isPinned: newPinnedStatus };
        }
        return chat;
      });

      setChatHistory(updatedHistory);
      toast({
        title: "Chat Updated",
        description: `Chat ${newPinnedStatus ? 'pinned' : 'unpinned'} successfully.`,
      });
    } catch (error) {
      console.error('Error toggling pin status:', error);
      toast({
        title: "Error",
        description: "Failed to update chat. Please try again.",
        variant: "destructive",
      });
    }
  };

  const deleteChat = async (chatId: string) => {
    try {
      // Delete from Firebase
      await deleteAIChat(chatId);

      // Update local state
      const updatedHistory = chatHistory.filter(chat => chat.id !== chatId);
      setChatHistory(updatedHistory);

      // If we're deleting the current chat, start a new one and navigate to /ai
      if (currentChatId === chatId) { // Removed selectedChatId from condition
        handleNewChat(); // This will also navigate to /ai
      }

      toast({
        title: "Chat Deleted",
        description: "Chat history deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting chat:', error);
      toast({
        title: "Error",
        description: "Failed to delete chat. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Function to scroll to discussion section
  const scrollToDiscussion = () => {
    if (discussionRef.current) {
      discussionRef.current.scrollIntoView({ behavior: 'smooth' });
      setDiscussionVisible(true);
    }
  };

  return (
    <div className="min-h-screen flex relative overflow-hidden bg-gradient-to-br from-background via-secondary to-background transition-colors duration-300">
      {/* Update Available Notification */}
      {updateAvailable && (
        <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-black p-3 text-center z-[1001] shadow-lg flex items-center justify-center">
          <Info className="h-5 w-5 mr-2" />
          <span>A new version is available.</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="ml-4 bg-black text-white hover:bg-gray-800"
          >
            Refresh to Update
          </Button>
        </div>
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-[85vw] sm:w-72 bg-black/30 border-r border-white/10 backdrop-blur-xl transform transition-transform duration-300 ease-in-out shadow-xl",
          sidebarOpen ? "translate-x-0" : "-translate-x-full",
          updateAvailable ? "pt-12" : "" // Adjust sidebar top padding if update banner is visible
        )}
      >
        <div className="p-3 md:p-4 flex flex-col h-full">
          <div className="flex items-center justify-between mb-4 md:mb-6">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 md:h-5 md:w-5 text-primary" />
              <h2 className="text-lg md:text-xl font-bold">Recent Chats</h2>
            </div>
            <div className="flex items-center space-x-1">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 md:h-10 md:w-10 rounded-full hover:bg-white/10"
                    aria-label="Sort chats"
                  >
                    <ArrowUpDown className="h-4 w-4 md:h-5 md:w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setSortBy("newest")}>
                    Newest First
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                    Oldest First
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("name")}>
                    By Name
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={toggleSidebar}
                variant="ghost"
                size="icon"
                className="h-8 w-8 md:h-10 md:w-10 rounded-full hover:bg-white/10"
                aria-label="Close sidebar"
              >
                <X className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </div>

          <div className="flex-grow overflow-auto">
            {isLoading ? (
              <div className="text-center py-8 text-muted-foreground text-sm">
                Loading chats...
              </div>
            ) : chatHistory.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground text-sm">
                No recent chats
              </div>
            ) : (
              <div className="space-y-4">
                {/* Pinned Chats Section */}
                {sortedChatHistory.some(chat => chat.isPinned) && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 px-2">
                      <Pin className="h-3 w-3 text-primary" />
                      <h3 className="text-xs font-medium text-primary">Pinned Chats</h3>
                    </div>
                    <div className="space-y-2">
                      {sortedChatHistory.filter(chat => chat.isPinned).map((chat) => (
                        <div
                          key={chat.id}
                          className="p-3 rounded-lg bg-primary/5 hover:bg-primary/10 cursor-pointer transition-all duration-200 border border-primary/20 hover:border-primary/40 group"
                          onClick={() => loadChat(chat.id)}
                        >
                          <div className="flex justify-between">
                            <p className="font-medium line-clamp-2 text-sm">{chat.preview}</p>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 hover:bg-primary/10"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  togglePinChat(chat.id);
                                }}
                              >
                                <PinOff className="h-3 w-3 text-primary" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(chat.timestamp).toLocaleDateString(undefined, {
                              month: 'short',
                              day: 'numeric'
                            })}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Regular Chats Section */}
                <div className="space-y-2">
                  {sortedChatHistory.filter(chat => !chat.isPinned).length > 0 && (
                    <div className="flex items-center gap-2 px-2">
                      <MessageSquare className="h-3 w-3 text-muted-foreground" />
                      <h3 className="text-xs font-medium text-muted-foreground">All Chats</h3>
                    </div>
                  )}
                  <div className="space-y-2 md:space-y-3">
                    {sortedChatHistory.filter(chat => !chat.isPinned).map((chat) => (
                      <div
                        key={chat.id}
                        className="p-2 md:p-3 rounded-lg bg-background/60 hover:bg-accent/10 cursor-pointer transition-all duration-200 border border-border/50 hover:border-primary/30 group"
                        onClick={() => loadChat(chat.id)}
                      >
                        <div className="flex justify-between">
                          <p className="font-medium line-clamp-2 text-xs md:text-sm">{chat.preview}</p>
                          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 hover:bg-primary/10"
                              onClick={(e) => {
                                e.stopPropagation();
                                togglePinChat(chat.id);
                              }}
                            >
                              <Pin className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className={`h-6 w-6 hover:bg-yellow-500/10 ${chat.isStarred ? "text-yellow-500" : ""}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleStarChat(chat.id);
                              }}
                            >
                              <Star className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 hover:bg-red-500/10"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteChat(chat.id);
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center gap-1 md:gap-2 mt-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(chat.timestamp).toLocaleDateString(undefined, {
                            month: 'short',
                            day: 'numeric'
                          })}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          <Button
            onClick={handleNewChat}
            variant="outline"
            className="mt-3 md:mt-4 w-full text-sm md:text-base py-1 md:py-2"
          >
            <Plus className="mr-1 md:mr-2 h-3 w-3 md:h-4 md:w-4" />
            New Chat
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col w-full max-w-full overflow-hidden">
        <main className={cn(
          "flex-1 mx-auto px-2 sm:px-4 md:px-8 py-4 md:py-16 relative w-full max-w-full overflow-hidden",
          updateAvailable ? "pt-20 md:pt-28" : "" // Adjust main content top padding
        )}>
          {/* Toggle sidebar button */}
          <button
            onClick={toggleSidebar}
            className={cn(
              "fixed top-4 md:top-8 left-4 md:left-8 z-40 p-2 md:p-3 rounded-full bg-background/50 hover:bg-primary/20 shadow-md hover:shadow-lg transition-all duration-300 backdrop-blur-md",
              sidebarOpen ? "opacity-0 pointer-events-none" : "opacity-100",
              updateAvailable ? "top-16 md:top-20" : "" // Adjust button position if update banner is visible
            )}
            aria-label="Toggle menu"
          >
            <Menu className="w-5 h-5" />
          </button>

          {/* Logo and branding */}
          <div className="flex flex-col items-center justify-center mb-6 md:mb-16 pt-8 md:pt-0 max-w-5xl mx-auto w-full">
            <Link to="/" className="flex items-center gap-2 sm:gap-3 md:gap-4 hover:opacity-80 transition-opacity">
              <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-8 h-8 sm:w-10 sm:h-10 md:w-16 md:h-16 rounded-full shadow-lg" />
              <h1 className="text-2xl sm:text-3xl md:text-7xl font-extrabold text-foreground font-sans tracking-tight">
                IsotopeAI
              </h1>
            </Link>
          </div>

          {/* Greeting and quote */}
          <div className="text-center mb-6 md:mb-12 max-w-5xl mx-auto w-full px-2">
            <div className="flex items-center justify-center gap-2 sm:gap-3 mb-1 md:mb-2">
              {(() => {
                const hour = new Date().getHours();
                return hour >= 5 && hour < 12 ? (
                  <Sun className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-yellow-500" />
                ) : hour >= 12 && hour < 18 ? (
                  <Sun className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-orange-500" />
                ) : (
                  hour < 22 ? <Sunset className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-indigo-500" /> : <Moon className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-indigo-700" />
                );
              })()}
              <h2 className="text-xl sm:text-2xl md:text-4xl font-bold greeting-text">
                {greeting}, <span className="text-primary">{user?.displayName || 'Friend'}</span>
              </h2>
            </div>
            <p className="text-muted-foreground text-xs sm:text-sm md:text-lg italic quote-text">{quote}</p>
          </div>

         {currentChatId ? (
           <div className="transition-all duration-500 ease-in-out transform scale-100 opacity-100 max-w-5xl mx-auto w-full px-2 sm:px-4">
             <ChatInterface
               initialQuery={initialQuery}
               initialImage={initialImage}
               initialChatId={currentChatId}
               currentChatId={currentChatId}
               isNewChat={isNewChatInProgress}
               onChatInitialized={handleChatInitialized}
             />
           </div>
         ) : (
           <div className="search-container flex justify-center mb-6 md:mb-12 animate-in fade-in slide-in-from-bottom-4 duration-500 max-w-5xl mx-auto w-full">
             <div className="w-full max-w-2xl transition-all duration-500 ease-in-out transform hover:scale-[1.01] text-center">
               <SearchBar onSubmit={handleSearchSubmit} />
               {/* Navigation Links */}
               <div className="mt-4 flex justify-center flex-wrap gap-2 md:gap-4 mx-auto navigation-links">
                 <Link
                   to="/groups"
                   className="px-4 py-3 sm:px-5 sm:py-3 md:px-4 md:py-2 bg-violet-500/10 hover:bg-violet-500/20 text-violet-400 rounded-lg text-sm sm:text-base md:text-base font-medium flex items-center gap-2 sm:gap-3 md:gap-2 transition-colors duration-200 border border-violet-500/20 nav-button"
                 >
                   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 sm:h-5 sm:w-5 md:h-4 md:w-4">
                     <path d="M18 8c0 2.2-1.8 4-4 4s-4-1.8-4-4 1.8-4 4-4 4 1.8 4 4zM6 15c0-2.2 1.8-4 4-4m8 0c2.2 0 4 1.8 4 4M3 8c0-2.2 1.8-4 4-4m14 0c2.2 0 4 1.8 4 4M3 22v-3c0-2.2 1.8-4 4-4h0m14 7v-3c0-2.2-1.8-4-4-4h-4" />
                   </svg>
                   <span>Groups</span>
                 </Link>
                 <Link
                   to="/productivity"
                   className="px-4 py-3 sm:px-5 sm:py-3 md:px-4 md:py-2 bg-rose-500/10 hover:bg-rose-500/20 text-rose-400 rounded-lg text-sm sm:text-base md:text-base font-medium flex items-center gap-2 sm:gap-3 md:gap-2 transition-colors duration-200 border border-rose-500/20 nav-button"
                 >
                   <Zap className="h-4 w-4 sm:h-5 sm:w-5 md:h-4 md:w-4" />
                   <span>Productivity</span>
                 </Link>
                 <Link
                   to="/mock-tests"
                   className="px-4 py-3 sm:px-5 sm:py-3 md:px-4 md:py-2 bg-cyan-500/10 hover:bg-cyan-500/20 text-cyan-400 rounded-lg text-sm sm:text-base md:text-base font-medium flex items-center gap-2 sm:gap-3 md:gap-2 transition-colors duration-200 border border-cyan-500/20 nav-button"
                 >
                   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 sm:h-5 sm:w-5 md:h-4 md:w-4">
                     <path d="M9 11V6a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-5" />
                     <path d="m9 15 2 2 4-4" />
                     <path d="M3 15H1.5a1.5 1.5 0 0 0 0 3h10a1.5 1.5 0 0 1 0 3H8" />
                   </svg>
                   <span>Mock Tests</span>
                 </Link>
                 <Link
                   to="/analytics"
                   className="px-4 py-3 sm:px-5 sm:py-3 md:px-4 md:py-2 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 rounded-lg text-sm sm:text-base md:text-base font-medium flex items-center gap-2 sm:gap-3 md:gap-2 transition-colors duration-200 border border-blue-500/20 nav-button"
                 >
                   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 sm:h-5 sm:w-5 md:h-4 md:w-4">
                     <path d="M21 21H3"></path>
                     <path d="M19 7l-3 5-4-7-4 7-3-5"></path>
                   </svg>
                   <span>Analytics</span>
                 </Link>
                 <Link
                   to="/tasks"
                   className="px-4 py-3 sm:px-5 sm:py-3 md:px-4 md:py-2 bg-emerald-500/10 hover:bg-emerald-500/20 text-emerald-400 rounded-lg text-sm sm:text-base md:text-base font-medium flex items-center gap-2 sm:gap-3 md:gap-2 transition-colors duration-200 border border-emerald-500/20 nav-button"
                 >
                   <ListTodo className="h-4 w-4 sm:h-5 sm:w-5 md:h-4 md:w-4" />
                   <span>Tasks</span>
                 </Link>
               </div>
               {/* Scroll indicator */}
               <div
                 className="mt-10 sm:mt-16 flex flex-col items-center text-muted-foreground/60 cursor-pointer hover:text-primary/70 transition-colors duration-200"
                 onClick={scrollToDiscussion}
               >
                 <span className="text-xs sm:text-sm mb-1">Scroll for community discussions</span>
                 <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5" />
               </div>
             </div>
           </div>
         )}

          {/* Community Discussion Section */}
          <div
            ref={discussionRef}
            className={cn(
              "mt-8 sm:mt-10 w-full max-w-[3000px] mx-auto px-0 transition-all duration-700 scroll-mt-10 discussion-section",
              discussionVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-20"
            )}
          >
            <DiscussionSection chatId="global-chat" />
          </div>
        </main>

        {/* Enhanced Footer that appears on scroll */}
        <motion.footer
          ref={footerRef}
          initial={{ opacity: 0, y: 50 }}
          animate={{
            opacity: footerVisible ? 1 : 0,
            y: footerVisible ? 0 : 50,
            transition: {
              duration: 0.7,
              ease: [0.25, 0.4, 0.25, 1]
            }
          }}
          className="relative z-10 border-t border-white/[0.08] bg-black/20 backdrop-blur-sm mt-16 dark:bg-black/20 light:bg-cream-50/80"
        >
          {/* Indian Flag Tricolor */}
          <div className="w-full flex flex-row h-4 relative z-20">
            <div className="w-1/3 h-full" style={{ backgroundColor: "#FF671F" }} /> {/* India Saffron */}
            <div className="w-1/3 h-full" style={{ backgroundColor: "#FFFFFF" }} /> {/* White */}
            <div className="w-1/3 h-full" style={{ backgroundColor: "#046A38" }} /> {/* India Green */}
          </div>

          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 pointer-events-none dark:to-black/50 light:to-cream-100/50" />

          {/* Decorative glow elements */}
          <div className="absolute bottom-0 left-1/4 w-64 h-32 bg-violet-500/10 rounded-full blur-3xl opacity-40 dark:bg-violet-500/10 light:bg-cream-200/50" />
          <div className="absolute bottom-0 right-1/4 w-64 h-32 bg-rose-500/10 rounded-full blur-3xl opacity-40 dark:bg-rose-500/10 light:bg-cream-300/50" />

          <div className="container mx-auto px-4 md:px-6 py-12 md:py-16 relative">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-start">
              {/* Logo and description */}
              <div className="md:col-span-4 flex flex-col space-y-6">
                <div className="flex items-center space-x-3">
                  <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-12 h-12 rounded-full border border-white/10 shadow-lg dark:border-white/10 light:border-cream-200" />
                  <span className="font-semibold text-2xl bg-clip-text text-transparent bg-gradient-to-r from-white/90 to-white/70 dark:from-white/90 dark:to-white/70 light:from-cream-800 light:to-cream-600">
                    IsotopeAI
                  </span>
                </div>

                <p className="text-white/40 max-w-md leading-relaxed dark:text-white/40 light:text-cream-700/80">
                  Your all-in-one platform for AI-powered learning, productivity tools, and collaborative study.
                  We help PCM students excel in their competitive exams through innovative technology and community support.
                </p>

                <div className="flex items-center space-x-4 pt-2">
                  <a
                    href="https://www.instagram.com/isotope.ai/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5 dark:text-white/40 dark:hover:text-white/90 dark:hover:bg-white/5 light:text-cream-700/60 light:hover:text-cream-800 light:hover:bg-cream-100/50"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-instagram"
                    >
                      <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                      <path d="M17.5 6.5h.01" />
                    </svg>
                  </a>
                  <a
                    href="https://www.reddit.com/r/Isotope/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5 dark:text-white/40 dark:hover:text-white/90 dark:hover:bg-white/5 light:text-cream-700/60 light:hover:text-cream-800 light:hover:bg-cream-100/50"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="12" cy="12" r="10" />
                      <circle cx="12" cy="9" r="1" />
                      <circle cx="12" cy="15" r="1" />
                      <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                      <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                      <path d="M7.5 13h9" />
                      <path d="M10 16v-3" />
                      <path d="M14 16v-3" />
                    </svg>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5 dark:text-white/40 dark:hover:text-white/90 dark:hover:bg-white/5 light:text-cream-700/60 light:hover:text-cream-800 light:hover:bg-cream-100/50"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect width="20" height="16" x="2" y="4" rx="2" />
                      <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                    </svg>
                  </a>
                </div>
              </div>

              {/* Features column */}
              <div className="md:col-span-2">
                <h3 className="text-white/90 font-semibold mb-4">Features</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/ai-landing" className="text-white/50 hover:text-white/90 transition-colors">AI Assistant</Link>
                  </li>
                  <li>
                    <Link to="/groups-landing" className="text-white/50 hover:text-white/90 transition-colors">Study Groups</Link>
                  </li>
                  <li>
                    <Link to="/productivity-landing" className="text-white/50 hover:text-white/90 transition-colors">Productivity</Link>
                  </li>
                  <li>
                    <Link to="/tasks-landing" className="text-white/50 hover:text-white/90 transition-colors">Task Management</Link>
                  </li>
                </ul>
              </div>

              {/* Resources column */}
              <div className="md:col-span-2">
                <h3 className="text-white/90 font-semibold mb-4">Resources</h3>
                <ul className="space-y-2">
                  <li>
                    <a href="https://isotopeai.featurebase.app/changelog" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Changelog</a>
                  </li>
                  <li>
                    <FeedbackWidget className="inline text-white/50 hover:text-white/90 transition-colors cursor-pointer" />
                  </li>
                  <li>
                    <a href="https://www.reddit.com/r/Isotope/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Community</a>
                  </li>
                </ul>
              </div>

              {/* Legal column */}
              <div className="md:col-span-2">
                <h3 className="text-white/90 font-semibold mb-4">Legal</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/privacy-policy" className="text-white/50 hover:text-white/90 transition-colors">Privacy Policy</Link>
                  </li>
                  <li>
                    <Link to="/terms-of-service" className="text-white/50 hover:text-white/90 transition-colors">Terms of Service</Link>
                  </li>
                </ul>
              </div>

              {/* Contact column */}
              <div className="md:col-span-2">
                <h3 className="text-white/90 font-semibold mb-4">Contact</h3>
                <ul className="space-y-2">
                  <li>
                    <a href="mailto:<EMAIL>" className="text-white/50 hover:text-white/90 transition-colors">Email Us</a>
                  </li>
                  <li>
                    <a href="https://www.instagram.com/isotope.ai/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Instagram</a>
                  </li>
                  <li>
                    <a href="https://www.reddit.com/r/Isotope/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Reddit</a>
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-12 pt-6 border-t border-white/[0.08] flex flex-col md:flex-row justify-between items-center">
              <p className="text-white/40 text-sm">
                © {new Date().getFullYear()} IsotopeAI. All rights reserved.
              </p>
              <div className="flex items-center gap-4 mt-2 md:mt-0">
                <Link to="/privacy-policy" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
                  Privacy Policy
                </Link>
                <span className="text-white/20">|</span>
                <Link to="/terms-of-service" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
                  Terms of Service
                </Link>
              </div>
            </div>
          </div>
        </motion.footer>
      </div>

      <SharePopup isOpen={isShareOpen} onClose={closeShare} />
    </div>
  );
};

export default AI;