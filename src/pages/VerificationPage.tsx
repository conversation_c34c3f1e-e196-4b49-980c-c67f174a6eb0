import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { checkEmailVerification, resendVerificationEmail, refreshUserSession } from '../utils/supabase';

const VerificationPage = () => {
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      navigate('/login', { replace: true });
      return;
    }

    // Check if user is already verified
    const checkInitialVerification = async () => {
      try {
        const { isVerified } = await checkEmailVerification();
        if (isVerified) {
          // User is already verified, redirect to profile setup
          navigate('/profile-setup', { replace: true });
        }
      } catch (error) {
        console.error('Error checking initial verification:', error);
      }
    };

    checkInitialVerification();
  }, [user, navigate]);

  const handleCheckVerification = async () => {
    if (!user) return;

    setIsLoading(true);
    setVerificationStatus(null);

    try {
      // Refresh the session to get the latest user data
      await refreshUserSession();

      // Check verification status
      const { isVerified } = await checkEmailVerification();

      if (isVerified) {
        toast({
          title: "Email Verified!",
          description: "Your email has been successfully verified.",
        });

        // Redirect to profile setup
        navigate('/profile-setup', { replace: true });
      } else {
        setVerificationStatus("Email not yet verified. Please check your inbox and click the verification link.");
        toast({
          title: "Not Verified",
          description: "Please check your email and click the verification link.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error checking verification:', error);
      setVerificationStatus("Error checking verification status. Please try again.");
      toast({
        title: "Error",
        description: "Failed to check verification status. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerificationEmail = async () => {
    if (!user?.email) return;

    setIsResending(true);
    setVerificationStatus(null);

    try {
      await resendVerificationEmail();
      setVerificationStatus("Verification email resent successfully! Please check your inbox.");
      toast({
        title: "Email Sent",
        description: "Verification email has been resent. Please check your inbox.",
      });
    } catch (error: any) {
      console.error('Error resending verification email:', error);
      const errorMessage = error.message || "Failed to resend verification email.";
      setVerificationStatus(`Error: ${errorMessage}`);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsResending(false);
    }
  };

  if (!user) {
    return null; // Handled by useEffect redirect
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-8 px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold mb-4">Verify Your Email</h1>
          <p className="text-gray-600 mb-2">
            We've sent a verification link to:
          </p>
          <p className="text-blue-600 font-semibold mb-4">
            {user.email}
          </p>
          <p className="text-gray-600 text-sm">
            Please click the link in your email to verify your account and continue.
          </p>
        </div>

        {verificationStatus && (
          <div className={`p-4 rounded-lg mb-6 ${
            verificationStatus.includes('Error') || verificationStatus.includes('not yet verified')
              ? 'bg-red-50 text-red-700 border border-red-200'
              : 'bg-green-50 text-green-700 border border-green-200'
          }`}>
            <p className="text-sm">{verificationStatus}</p>
          </div>
        )}

        <div className="space-y-3">
          <Button
            onClick={handleCheckVerification}
            disabled={isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Checking...
              </>
            ) : (
              "Verified? Continue"
            )}
          </Button>

          <Button
            onClick={handleResendVerificationEmail}
            disabled={isResending || isLoading}
            variant="outline"
            className="w-full"
            size="lg"
          >
            {isResending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600 mr-2"></div>
                Sending...
              </>
            ) : (
              "Resend Verification Email"
            )}
          </Button>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Didn't receive the email? Check your spam folder or try resending.
          </p>
        </div>
      </div>
    </div>
  );
};

export default VerificationPage;
