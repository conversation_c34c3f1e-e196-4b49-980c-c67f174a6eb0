import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { Button } from "@/components/ui/button";

const VerificationPage = () => {
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      navigate('/login', { replace: true });
    }
  }, [user, navigate]);

  const handleCheckVerification = async () => {
    setIsLoading(true);
    setVerificationStatus(null); // Reset status

    // Reload the user object to get the latest email_confirmed_at status
    // This might involve calling supabase.auth.refreshSession() or similar
    // For now, let's just refresh the page as a simple workaround
    window.location.reload();

    // After the reload, the onAuthStateChange listener in SupabaseAuthContext
    // will handle the redirection based on the updated user object

    setIsLoading(false);
  };

  const handleResendVerificationEmail = async () => {
    setIsLoading(true);
    // Implement logic to resend verification email using Supabase
    // This might involve calling supabase.auth.resendVerificationEmail() or similar
    // For now, let's just show a message
    setVerificationStatus("Verification email resent. Please check your inbox.");
    setIsLoading(false);
  };

  if (!user) {
    return null; // Handled by useEffect redirect
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-2">
      <h1 className="text-2xl font-bold mb-4">Verify Your Email</h1>
      <p className="text-gray-700 mb-4">
        A verification link has been sent to your email address. Please click the link to verify your email.
      </p>
      {verificationStatus && (
        <p className="text-green-500 mb-4">{verificationStatus}</p>
      )}
      <div className="space-x-4">
        <Button
          onClick={handleCheckVerification}
          disabled={isLoading}
        >
          {isLoading ? "Checking..." : "I've Verified My Email"}
        </Button>
        <Button
          onClick={handleResendVerificationEmail}
          disabled={isLoading}
          variant="secondary"
        >
          Resend Verification Email
        </Button>
      </div>
    </div>
  );
};

export default VerificationPage;
