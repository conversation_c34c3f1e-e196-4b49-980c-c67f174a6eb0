// Add Featurebase to the window object type
declare global {
  interface Window {
    Featurebase?: (
      action: string,
      payload: {
        organization: string;
        email?: string;
        name?: string;
        userId: string;
        profilePicture?: string;
      },
      callback?: (error?: Error) => void
    ) => void;
  }
}

import { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import {
  signInWithGoogle as supabaseSignInWithGoogle,
  signInWithEmailPassword as supabaseSignInWithEmailPassword,
  signUpWithEmailPassword as supabaseSignUpWithEmailPassword,
  signOut as supabaseSignOut,
  onAuthStateChange,
  getCurrentUser,
  getUserProfile,
  createUserProfile,
  checkEmailVerification,
} from '../utils/supabase';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSupabaseUserStore } from '../stores/supabaseUserStore';
import type { Database } from '../integrations/supabase/types';

type UserRow = Database['public']['Tables']['users']['Row'];

type OAuthResponse = {
  provider: string;
  url: string | null;
};

type AuthResponse = {
  user: User | null;
  session: Session | null;
};

interface SignInResult {
  user: User | null;
  session: Session | null;
  hasProfile: boolean;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signInWithGoogle: () => Promise<SignInResult | null>;
  signInWithEmailPassword: (email: string, password: string) => Promise<SignInResult>;
  signUpWithEmailPassword: (email: string, password: string) => Promise<SignInResult>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Function to identify user in Featurebase
const identifyFeaturebaseUser = (user: User, userProfile: UserRow | null) => {
  if (typeof window.Featurebase === 'function') {
    window.Featurebase(
      "identify",
      {
        organization: "isotopeai",
        email: user.email || undefined,
        name: userProfile?.username || user.user_metadata?.full_name || user.user_metadata?.name || undefined,
        userId: user.id,
        profilePicture: user.user_metadata?.avatar_url || undefined,
      },
      (err) => {
        if (err) {
          console.error("Featurebase identification error:", err);
        }
      }
    );
  }
};

export function SupabaseAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserProfile } = useSupabaseUserStore();

  useEffect(() => {
    let mounted = true;

    // Get initial user
    const getInitialUser = async () => {
      try {
        const { data: { user } } = await getCurrentUser();
        if (mounted) {
          setUser(user);
          if (user) {
            await handleUserAuthentication(user);
          }
        }
      } catch (error) {
        console.error('Error getting initial user:', error);
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    // Set up auth state listener
    const { data: { subscription } } = onAuthStateChange(async (currentUser) => {
      if (!mounted) return;

      console.log("Auth state changed. Current user:", currentUser?.id);
      setUser(currentUser);

      if (currentUser) {
        // Check email verification status first
        const { isVerified } = await checkEmailVerification();

        // Skip verification check for certain paths
        const isOnVerificationPage = location.pathname === '/verification';
        const isOnAuthCallback = location.pathname === '/auth/callback';

        if (!isVerified && !isOnVerificationPage && !isOnAuthCallback) {
          // Email not verified, redirect to verification page
          navigate('/verification', { replace: true });
          setLoading(false);
          return;
        }

        // If email is verified, proceed with profile check
        if (isVerified) {
          const userProfile = await handleUserAuthentication(currentUser);
          identifyFeaturebaseUser(currentUser, userProfile);

          const hasProfileUsername = userProfile && userProfile.username;
          const isOnProfileSetup = location.pathname === '/profile-setup';

          if (!hasProfileUsername) {
            // Profile not set up
            if (!isOnProfileSetup) {
              navigate('/profile-setup', { replace: true });
            }
          } else {
            navigate('/ai', { replace: true });
          }
        }
      } else {
        // User is null (logged out)
        setUserProfile(null);
      }

      setLoading(false);
    });

    getInitialUser();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [navigate, location.pathname, setUserProfile]);

  const handleUserAuthentication = async (user: User) => {
    try {
      // Get or create user profile
      let userProfile = await getUserProfile(user.id);
      
      if (!userProfile) {
        // Create new user profile
        userProfile = await createUserProfile(user);
      }

      // Update user store
      setUserProfile(userProfile);
      return userProfile;
    } catch (error) {
      console.error('Error handling user authentication:', error);
      return null;
    }
  };

  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      
      // Check for returnUrl in query parameters
      const searchParams = new URLSearchParams(location.search);
      const returnUrl = searchParams.get('returnUrl');

      const response = await supabaseSignInWithGoogle();
      
      // Type guard to check if this is an OAuth URL response
      if ('provider' in response && 'url' in response) {
        const { url } = response as OAuthResponse;
        if (url) {
          // The OAuth flow will continue after redirect
          window.location.href = url;
          return null;
        }
        throw new Error('No OAuth URL provided');
      }

      // At this point, response should be an AuthResponse
      const { user, session } = response as AuthResponse;
      if (user) {
        setUser(user);

        // Check email verification status
        const { isVerified } = await checkEmailVerification();

        if (!isVerified) {
          // Email not verified, redirect to verification page
          navigate('/verification', { replace: true });
          return { user, session, hasProfile: false };
        }

        // Email is verified, proceed with profile check
        const userProfile = await handleUserAuthentication(user);
        identifyFeaturebaseUser(user, userProfile);

        if (!userProfile || !userProfile.username) {
          navigate('/profile-setup', { replace: true });
          return { user, session, hasProfile: false };
        } else {
          navigate('/ai', { replace: true });
          return { user, session, hasProfile: true };
        }
      }

      return null;
    } catch (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithEmailPassword = async (email: string, password: string) => {
    try {
      setLoading(true);
      const resultData = await supabaseSignInWithEmailPassword(email, password);

      if (resultData?.user) {
        const user = resultData.user;
        const session = resultData.session;
        setUser(user);

        // Check email verification status
        const { isVerified } = await checkEmailVerification();

        if (!isVerified) {
          // Email not verified, redirect to verification page
          navigate('/verification', { replace: true });
          return { user, session, hasProfile: false };
        }

        // Email is verified, proceed with profile check
        const userProfile = await handleUserAuthentication(user);
        identifyFeaturebaseUser(user, userProfile);

        if (!userProfile || !userProfile.username) {
          navigate('/profile-setup', { replace: true });
          return { user, session, hasProfile: false };
        } else {
          navigate('/ai', { replace: true });
          return { user, session, hasProfile: true };
        }
      }
      throw new Error('Failed to sign in');
    } catch (error) {
      console.error('Error signing in with email/password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmailPassword = async (email: string, password: string) => {
    try {
      setLoading(true);
      const resultData = await supabaseSignUpWithEmailPassword(email, password);

      if (resultData?.user) {
        const user = resultData.user;
        const session = resultData.session;
        setUser(user);

        // For email signup, always redirect to verification page first
        navigate('/verification', { replace: true });
        return { user, session, hasProfile: false };
      }
      throw new Error('Failed to sign up');
    } catch (error) {
      console.error('Error signing up with email/password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await supabaseSignOut();
      setUser(null);
      setUserProfile(null);
      navigate('/', { replace: true });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signInWithGoogle,
    signInWithEmailPassword,
    signUpWithEmailPassword,
    signOut
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

export function useSupabaseAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return context;
}
